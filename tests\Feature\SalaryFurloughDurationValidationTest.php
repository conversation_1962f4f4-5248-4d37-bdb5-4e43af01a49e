<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\Time\TimeRequestValidationService;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Time\RequestConfig\RequestConfigDtl;
use Mockery;

class SalaryFurloughDurationValidationTest extends TestCase
{
    private TimeRequestValidationService $validationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->validationService = new TimeRequestValidationService();
    }

    /** @test */
    public function it_validates_salary_furlough_duration_method_directly()
    {
        // Test the validateSalaryFurloughDuration method directly
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateSalaryFurloughDuration');
        $method->setAccessible(true);

        // Test valid duration - exactly 1 day
        $countData = [
            'static_unit' => 'day',
            'chrono_unit' => 'day',
            'chrono_value' => 1,
            'count' => 1
        ];

        $result = $method->invoke($service, '2024-02-15', '2024-02-16', $countData, 91);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test invalid duration - 2 days instead of 1
        $result = $method->invoke($service, '2024-02-15', '2024-02-17', $countData, 91);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('яг 1 өдөр байх ёстой', $result['message']);
        $this->assertStringContainsString('Одоогийн зөрүү: 2 өдөр', $result['message']);
    }

    /** @test */
    public function it_validates_salary_furlough_duration_with_hours()
    {
        // Test the validateSalaryFurloughDuration method with hour precision
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateSalaryFurloughDuration');
        $method->setAccessible(true);

        // Test valid duration - exactly 4 hours
        $countData = [
            'static_unit' => 'day',
            'chrono_unit' => 'hour',
            'chrono_value' => 4,
            'count' => 2
        ];

        $result = $method->invoke($service, '2024-02-15 09:00:00', '2024-02-15 13:00:00', $countData, 93);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test invalid duration - 6 hours instead of 4
        $result = $method->invoke($service, '2024-02-15 09:00:00', '2024-02-15 15:00:00', $countData, 93);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('яг 4 цаг байх ёстой', $result['message']);
        $this->assertStringContainsString('Одоогийн зөрүү: 6 цаг', $result['message']);
    }

    /** @test */
    public function it_validates_salary_furlough_duration_with_multiple_days()
    {
        // Test the validateSalaryFurloughDuration method with multiple days
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateSalaryFurloughDuration');
        $method->setAccessible(true);

        // Test valid duration - exactly 2 days
        $countData = [
            'static_unit' => 'month',
            'chrono_unit' => 'day',
            'chrono_value' => 2,
            'count' => 1
        ];

        $result = $method->invoke($service, '2024-02-15', '2024-02-17', $countData, 92);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test invalid duration - 1 day instead of 2
        $result = $method->invoke($service, '2024-02-15', '2024-02-16', $countData, 92);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('яг 2 өдөр байх ёстой', $result['message']);
        $this->assertStringContainsString('Одоогийн зөрүү: 1 өдөр', $result['message']);
    }

    /** @test */
    public function it_validates_salary_furlough_duration_with_halfday()
    {
        // Test the validateSalaryFurloughDuration method with halfday precision
        $service = new TimeRequestValidationService();
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('validateSalaryFurloughDuration');
        $method->setAccessible(true);

        // Test valid duration - exactly 1 halfday (12 hours)
        $countData = [
            'static_unit' => 'quarter',
            'chrono_unit' => 'halfday',
            'chrono_value' => 1,
            'count' => 4
        ];

        $result = $method->invoke($service, '2024-02-15 08:00:00', '2024-02-15 20:00:00', $countData, 94);
        $this->assertTrue($result['valid']);
        $this->assertEquals('', $result['message']);

        // Test invalid duration - 2 halfdays instead of 1
        $result = $method->invoke($service, '2024-02-15 08:00:00', '2024-02-16 08:00:00', $countData, 94);
        $this->assertFalse($result['valid']);
        $this->assertStringContainsString('яг 1 хагас өдөр байх ёстой', $result['message']);
        $this->assertStringContainsString('Одоогийн зөрүү: 2 хагас өдөр', $result['message']);
    }
}
